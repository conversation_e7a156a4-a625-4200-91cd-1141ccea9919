import type { NextApiRequest, NextApiResponse } from 'next';
import { verifyAdminAuth } from '../../../../../lib/auth/admin-auth';
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;
const supabase = createClient(supabaseUrl, supabaseKey);

interface PushSubscriptionData {
  endpoint: string;
  keys: {
    p256dh: string;
    auth: string;
  };
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  const requestId = Math.random().toString(36).substring(2, 8);
  console.log(`[${requestId}] Push subscription API called - ${req.method}`);

  if (req.method !== 'POST') {
    return res.status(405).json({ 
      error: 'Method not allowed',
      requestId 
    });
  }

  try {
    // Verify admin authentication
    const authResult = await verifyAdminAuth(req);
    if (!authResult.success) {
      console.log(`[${requestId}] Authentication failed:`, authResult.message);
      return res.status(401).json({
        error: 'Unauthorized',
        message: authResult.message,
        requestId
      });
    }

    const { user } = authResult;

    if (!user || !user.id) {
      console.log(`[${requestId}] User object missing or invalid:`, user);
      return res.status(401).json({
        error: 'Invalid user data',
        message: 'User information is missing or invalid',
        requestId
      });
    }

    const subscriptionData: PushSubscriptionData = req.body;

    // Validate required fields
    if (!subscriptionData.endpoint || !subscriptionData.keys?.p256dh || !subscriptionData.keys?.auth) {
      console.log(`[${requestId}] Invalid subscription data:`, subscriptionData);
      return res.status(400).json({
        error: 'Invalid subscription data',
        message: 'endpoint, keys.p256dh, and keys.auth are required',
        requestId
      });
    }

    // Get user agent and IP for tracking
    const userAgent = req.headers['user-agent'] || '';
    const ipAddress = req.headers['x-forwarded-for'] || req.connection.remoteAddress || '';

    // Check if subscription already exists for this user and endpoint
    const { data: existingSubscription } = await supabase
      .from('push_subscriptions')
      .select('id, is_active')
      .eq('user_id', user.id)
      .eq('endpoint', subscriptionData.endpoint)
      .single();

    if (existingSubscription) {
      // Update existing subscription
      const { data: updatedSubscription, error: updateError } = await supabase
        .from('push_subscriptions')
        .update({
          p256dh_key: subscriptionData.keys.p256dh,
          auth_key: subscriptionData.keys.auth,
          user_agent: userAgent,
          ip_address: ipAddress,
          is_active: true,
          last_used: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('id', existingSubscription.id)
        .select()
        .single();

      if (updateError) {
        console.error(`[${requestId}] Failed to update push subscription:`, updateError);
        return res.status(500).json({
          error: 'Failed to update push subscription',
          message: updateError.message,
          requestId
        });
      }

      console.log(`[${requestId}] Push subscription updated for user ${user.email}`);
      return res.status(200).json({
        success: true,
        message: 'Push subscription updated successfully',
        subscription: updatedSubscription,
        requestId
      });
    } else {
      // Create new subscription
      const { data: newSubscription, error: insertError } = await supabase
        .from('push_subscriptions')
        .insert({
          user_id: user.id,
          endpoint: subscriptionData.endpoint,
          p256dh_key: subscriptionData.keys.p256dh,
          auth_key: subscriptionData.keys.auth,
          user_agent: userAgent,
          ip_address: ipAddress,
          is_active: true,
          last_used: new Date().toISOString()
        })
        .select()
        .single();

      if (insertError) {
        console.error(`[${requestId}] Failed to create push subscription:`, insertError);
        return res.status(500).json({
          error: 'Failed to create push subscription',
          message: insertError.message,
          requestId
        });
      }

      console.log(`[${requestId}] Push subscription created for user ${user.email}`);
      return res.status(201).json({
        success: true,
        message: 'Push subscription created successfully',
        subscription: newSubscription,
        requestId
      });
    }

  } catch (error) {
    console.error(`[${requestId}] Push subscription API error:`, error);
    return res.status(500).json({
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error',
      requestId
    });
  }
}
