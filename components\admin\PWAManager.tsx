/**
 * Ocean Soul Sparkles Admin - PWA Manager Component
 * Handles PWA initialization, service worker registration, and offline capabilities
 */

import React, { useEffect, useState } from 'react';
import { cacheManager } from '../../lib/pwa/cache-manager';
import { pushNotificationManager } from '../../lib/notifications/push-notifications';
import { errorManager } from '../../lib/error-handling/error-manager';

interface PWAManagerProps {
  children?: React.ReactNode;
}

interface PWAStatus {
  serviceWorkerRegistered: boolean;
  cacheInitialized: boolean;
  pushNotificationsEnabled: boolean;
  isOnline: boolean;
  installPromptAvailable: boolean;
}

export default function PWAManager({ children }: PWAManagerProps) {
  const [pwaStatus, setPwaStatus] = useState<PWAStatus>({
    serviceWorkerRegistered: false,
    cacheInitialized: false,
    pushNotificationsEnabled: false,
    isOnline: true, // Will be updated on client side
    installPromptAvailable: false
  });
  const [deferredPrompt, setDeferredPrompt] = useState<any>(null);
  const [showInstallBanner, setShowInstallBanner] = useState(false);

  useEffect(() => {
    // Only run on client side
    if (typeof window !== 'undefined') {
      // Update online status
      setPwaStatus(prev => ({
        ...prev,
        isOnline: navigator.onLine
      }));

      initializePWA();
      setupEventListeners();

      return () => {
        removeEventListeners();
      };
    }
  }, []);

  /**
   * Initialize PWA features
   */
  const initializePWA = async () => {
    try {
      // Register service worker
      await registerServiceWorker();
      
      // Initialize cache manager
      await initializeCacheManager();
      
      // Initialize push notifications
      await initializePushNotifications();
      
      // Check if app is already installed
      checkInstallStatus();
      
      console.log('PWA initialization complete');
    } catch (error) {
      console.error('PWA initialization failed:', error);
    }
  };

  /**
   * Register service worker
   */
  const registerServiceWorker = async (): Promise<void> => {
    if (typeof window !== 'undefined' && 'serviceWorker' in navigator) {
      try {
        const registration = await navigator.serviceWorker.register('/sw.js', {
          scope: '/admin'
        });

        console.log('Service Worker registered:', registration);

        setPwaStatus(prev => ({
          ...prev,
          serviceWorkerRegistered: true
        }));

        // Listen for service worker updates
        registration.addEventListener('updatefound', () => {
          const newWorker = registration.installing;
          if (newWorker) {
            newWorker.addEventListener('statechange', () => {
              if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                // New service worker is available
                showUpdateAvailableNotification();
              }
            });
          }
        });

      } catch (error) {
        console.error('Service Worker registration failed:', error);
        errorManager.handlePWAError(error, 'Service Worker Registration');
      }
    }
  };

  /**
   * Initialize cache manager
   */
  const initializeCacheManager = async (): Promise<void> => {
    try {
      await cacheManager.initialize();
      
      setPwaStatus(prev => ({
        ...prev,
        cacheInitialized: true
      }));

      // Pre-cache critical data
      await preCacheCriticalData();
      
    } catch (error) {
      console.error('Cache manager initialization failed:', error);
    }
  };

  /**
   * Initialize push notifications
   */
  const initializePushNotifications = async (): Promise<void> => {
    try {
      const success = await pushNotificationManager.initialize();
      
      setPwaStatus(prev => ({
        ...prev,
        pushNotificationsEnabled: success
      }));
      
    } catch (error) {
      console.error('Push notifications initialization failed:', error);
      errorManager.handlePWAError(error, 'Push Notifications Initialization');
    }
  };

  /**
   * Pre-cache critical data for offline use
   */
  const preCacheCriticalData = async (): Promise<void> => {
    try {
      // Cache customers data
      const customersResponse = await fetch('/api/admin/customers');
      if (customersResponse.ok) {
        const customersData = await customersResponse.json();
        const customers = customersData.customers || [];
        if (Array.isArray(customers)) {
          await cacheManager.cacheCustomerData(customers);
        }
      }

      // Cache services data
      const servicesResponse = await fetch('/api/admin/services');
      if (servicesResponse.ok) {
        const services = await servicesResponse.json();
        // TODO: Store services in cache when cache method is available
        console.log('Services data fetched for caching:', services.length);
      }

      console.log('Critical data pre-cached for offline use');
    } catch (error) {
      console.error('Failed to pre-cache critical data:', error);
    }
  };

  /**
   * Setup event listeners
   */
  const setupEventListeners = (): void => {
    if (typeof window !== 'undefined') {
      // Online/offline status
      window.addEventListener('online', handleOnlineStatusChange);
      window.addEventListener('offline', handleOnlineStatusChange);

      // Install prompt
      window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);

      // App installed
      window.addEventListener('appinstalled', handleAppInstalled);

      // Service worker message handling
      if ('serviceWorker' in navigator) {
        navigator.serviceWorker.addEventListener('message', handleServiceWorkerMessage);
        navigator.serviceWorker.addEventListener('controllerchange', handleControllerChange);
      }
    }
  };

  /**
   * Remove event listeners
   */
  const removeEventListeners = (): void => {
    if (typeof window !== 'undefined') {
      window.removeEventListener('online', handleOnlineStatusChange);
      window.removeEventListener('offline', handleOnlineStatusChange);
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
      window.removeEventListener('appinstalled', handleAppInstalled);

      // Service worker message handling
      if ('serviceWorker' in navigator) {
        navigator.serviceWorker.removeEventListener('message', handleServiceWorkerMessage);
        navigator.serviceWorker.removeEventListener('controllerchange', handleControllerChange);
      }
    }
  };

  /**
   * Handle online/offline status changes
   */
  const handleOnlineStatusChange = (): void => {
    if (typeof window !== 'undefined') {
      const isOnline = navigator.onLine;

      setPwaStatus(prev => ({
        ...prev,
        isOnline
      }));

      if (isOnline) {
        // Sync offline data when back online
        syncOfflineData();
        showOnlineNotification();
      } else {
        showOfflineNotification();
      }
    }
  };

  /**
   * Handle install prompt
   */
  const handleBeforeInstallPrompt = (event: Event): void => {
    event.preventDefault();
    setDeferredPrompt(event);
    
    setPwaStatus(prev => ({
      ...prev,
      installPromptAvailable: true
    }));

    // Show install banner after a delay
    setTimeout(() => {
      setShowInstallBanner(true);
    }, 5000);
  };

  /**
   * Handle app installed
   */
  const handleAppInstalled = (): void => {
    console.log('PWA was installed');
    setDeferredPrompt(null);
    setShowInstallBanner(false);

    setPwaStatus(prev => ({
      ...prev,
      installPromptAvailable: false
    }));
  };

  /**
   * Handle service worker messages with proper error handling
   */
  const handleServiceWorkerMessage = (event: MessageEvent): void => {
    try {
      console.log('Service Worker message received:', event.data);

      if (event.data && event.data.type) {
        switch (event.data.type) {
          case 'SKIP_WAITING_RESPONSE':
            console.log('Service worker skip waiting completed');
            break;
          case 'VERSION_RESPONSE':
            console.log('Service worker version:', event.data.version);
            break;
          case 'MESSAGE_RECEIVED':
            console.log('Service worker acknowledged message:', event.data.originalType);
            break;
          case 'ERROR_RESPONSE':
            console.error('Service worker error:', event.data.error);
            break;
          default:
            console.log('Unknown service worker message type:', event.data.type);
        }
      }
    } catch (error) {
      console.error('Error handling service worker message:', error);
    }
  };

  /**
   * Handle service worker controller changes
   */
  const handleControllerChange = (): void => {
    console.log('Service worker controller changed');
    // Optionally reload the page or show update notification
    if (pwaStatus.serviceWorkerRegistered) {
      showUpdateAvailableNotification();
    }
  };

  /**
   * Send message to service worker with proper MessageChannel handling
   */
  const sendMessageToServiceWorker = async (message: any, timeout: number = 5000): Promise<any> => {
    return new Promise((resolve, reject) => {
      if (!('serviceWorker' in navigator) || !navigator.serviceWorker.controller) {
        reject(new Error('Service worker not available'));
        return;
      }

      // Create MessageChannel for two-way communication
      const messageChannel = new MessageChannel();
      const timeoutId = setTimeout(() => {
        reject(new Error('Service worker message timeout'));
      }, timeout);

      // Listen for response
      messageChannel.port1.onmessage = (event) => {
        clearTimeout(timeoutId);
        resolve(event.data);
      };

      // Send message with port for response
      try {
        navigator.serviceWorker.controller.postMessage(message, [messageChannel.port2]);
      } catch (error) {
        clearTimeout(timeoutId);
        reject(error);
      }
    });
  };

  /**
   * Trigger app installation
   */
  const installApp = async (): Promise<void> => {
    if (deferredPrompt) {
      deferredPrompt.prompt();
      const { outcome } = await deferredPrompt.userChoice;
      
      if (outcome === 'accepted') {
        console.log('User accepted the install prompt');
      } else {
        console.log('User dismissed the install prompt');
      }
      
      setDeferredPrompt(null);
      setShowInstallBanner(false);
    }
  };

  /**
   * Sync offline data
   */
  const syncOfflineData = async (): Promise<void> => {
    try {
      await cacheManager.registerBackgroundSync('background-sync-pos');
      await cacheManager.registerBackgroundSync('background-sync-bookings');
      console.log('Background sync registered');
    } catch (error) {
      console.error('Failed to sync offline data:', error);
    }
  };

  /**
   * Check if app is already installed
   */
  const checkInstallStatus = (): void => {
    if (typeof window !== 'undefined') {
      // Check if running in standalone mode (installed)
      const isStandalone = window.matchMedia('(display-mode: standalone)').matches;
      const isIOSStandalone = (window.navigator as any).standalone === true;

      if (isStandalone || isIOSStandalone) {
        console.log('App is running in installed mode');
      }
    }
  };

  /**
   * Show update available notification
   */
  const showUpdateAvailableNotification = (): void => {
    if (pwaStatus.pushNotificationsEnabled) {
      pushNotificationManager.showNotification({
        title: 'Update Available',
        body: 'A new version of Ocean Soul Sparkles Admin is available. Refresh to update.',
        tag: 'app-update',
        requireInteraction: true,
        actions: [
          { action: 'refresh', title: 'Refresh Now' },
          { action: 'dismiss', title: 'Later' }
        ]
      });
    }
  };

  /**
   * Show online notification
   */
  const showOnlineNotification = (): void => {
    if (pwaStatus.pushNotificationsEnabled) {
      pushNotificationManager.showNotification({
        title: 'Back Online',
        body: 'Connection restored. Syncing offline data...',
        tag: 'online-status',
        silent: true
      });
    }
  };

  /**
   * Show offline notification
   */
  const showOfflineNotification = (): void => {
    if (pwaStatus.pushNotificationsEnabled) {
      pushNotificationManager.showNotification({
        title: 'Offline Mode',
        body: 'You are now offline. Some features may be limited.',
        tag: 'offline-status',
        silent: true
      });
    }
  };

  return (
    <>
      {children}
      
      {/* Install Banner */}
      {showInstallBanner && (
        <div style={{
          position: 'fixed',
          bottom: '20px',
          left: '20px',
          right: '20px',
          background: '#16213e',
          color: 'white',
          padding: '16px',
          borderRadius: '8px',
          boxShadow: '0 4px 12px rgba(0,0,0,0.3)',
          zIndex: 10000,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between'
        }}>
          <div>
            <strong>Install Ocean Soul Sparkles Admin</strong>
            <p style={{ margin: '4px 0 0 0', fontSize: '14px', opacity: 0.9 }}>
              Add to home screen for quick access
            </p>
          </div>
          <div style={{ display: 'flex', gap: '8px' }}>
            <button
              onClick={installApp}
              style={{
                background: '#4CAF50',
                color: 'white',
                border: 'none',
                padding: '8px 16px',
                borderRadius: '4px',
                cursor: 'pointer'
              }}
            >
              Install
            </button>
            <button
              onClick={() => setShowInstallBanner(false)}
              style={{
                background: 'transparent',
                color: 'white',
                border: '1px solid rgba(255,255,255,0.3)',
                padding: '8px 16px',
                borderRadius: '4px',
                cursor: 'pointer'
              }}
            >
              Later
            </button>
          </div>
        </div>
      )}

      {/* Offline Indicator */}
      {!pwaStatus.isOnline && (
        <div style={{
          position: 'fixed',
          top: '0',
          left: '0',
          right: '0',
          background: '#f44336',
          color: 'white',
          padding: '8px',
          textAlign: 'center',
          fontSize: '14px',
          zIndex: 10001
        }}>
          📱 Offline Mode - Some features may be limited
        </div>
      )}
    </>
  );
}
